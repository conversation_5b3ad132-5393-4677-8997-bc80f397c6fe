import { Badge } from "@heroui/badge";
import { Card, CardBody } from "@heroui/card";
import Image from "next/image";
import Link from "next/link";

interface PhotoPack {
  id: number | undefined;
  title: string;
  description: string;
  image: string;
  tag?: string;
  base_tune_id: number | undefined;
}

interface PhotoPackCardProps {
  pack: PhotoPack;
  priority?: boolean;
}

export function PhotoPackCard({ pack, priority = false }: PhotoPackCardProps) {
  return (
    <Link href={`/packs/${pack.id}`} className="">
      <Card className="overflow-hidden group transition-all duration-300 hover:shadow-lg hover:-translate-y-1 cursor-pointer">
        <div className="relative h-80 w-full overflow-hidden">
          <Image
            src={pack.image || "/app/icon.png"}
            alt={pack.title}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            className="object-center object-cover transition-transform duration-500 group-hover:scale-110 bg-gradient-to-br from-violet-700 to-rose-500"
            priority={priority}
          />

          {pack.tag && (
            <div className="absolute top-3 right-3 z-10 bg-white/80 text-black hover:bg-white/90 text-xs font-medium rounded border p-1">
              {pack.tag}
            </div>
          )}
        </div>
        <CardBody className="p-4">
          <h3 className="font-semibold text-lg mb-1">{pack.title}</h3>
          <p className="text-sm text-gray-500">{pack.description}</p>
        </CardBody>
      </Card>
    </Link>
  );
}
