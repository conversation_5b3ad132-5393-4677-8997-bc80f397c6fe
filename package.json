{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint --fix"}, "dependencies": {"@heroui/accordion": "^2.2.19", "@heroui/avatar": "^2.2.18", "@heroui/badge": "^2.2.14", "@heroui/button": "2.2.22", "@heroui/card": "^2.2.21", "@heroui/code": "2.2.16", "@heroui/dropdown": "^2.3.22", "@heroui/input": "2.4.22", "@heroui/kbd": "2.2.17", "@heroui/link": "2.2.19", "@heroui/listbox": "2.3.21", "@heroui/navbar": "2.2.20", "@heroui/select": "^2.4.22", "@heroui/snippet": "2.2.23", "@heroui/switch": "2.2.20", "@heroui/system": "2.4.18", "@heroui/theme": "2.4.17", "@heroui/toast": "^2.0.12", "@react-aria/ssr": "3.9.9", "@react-aria/visually-hidden": "3.8.24", "@react-email/components": "^0.2.0", "@stripe/stripe-js": "^7.4.0", "axios": "^1.10.0", "clsx": "2.1.1", "firebase": "^11.10.0", "framer-motion": "11.13.1", "intl-messageformat": "10.7.16", "lucide-react": "^0.525.0", "next": "15.3.1", "next-themes": "0.4.6", "nodemailer": "^7.0.5", "react": "18.3.1", "react-dom": "18.3.1", "react-icons": "^5.5.0", "resend": "^4.6.0", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/compat": "1.2.8", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.1", "@next/eslint-plugin-next": "15.3.1", "@react-types/shared": "3.30.0", "@types/axios": "^0.9.36", "@types/node": "22.15.3", "@types/nodemailer": "^6.4.17", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "8.31.1", "autoprefixer": "10.4.21", "eslint": "9.25.1", "eslint-config-next": "15.3.1", "eslint-config-prettier": "10.1.2", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.0.0", "postcss": "8.5.3", "prettier": "3.5.3", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3"}}