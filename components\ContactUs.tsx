"use client";

import React from "react";
import { But<PERSON> } from "@heroui/button";
import { Select, SelectItem } from "@heroui/select";
import { Input } from "@heroui/input";
import { Textarea } from "@heroui/input";

const ContactUs = () => {
  return (
    <div className="w-full flex items-center flex-col justify-center px-4 py-16 lg:px-8 lg:py-24 gap-12">
      <div className="text-center space-y-4 max-w-3xl">
        <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-primary to-secondary/70 bg-clip-text text-transparent">
          Contact Us
        </h1>
        <p className="text-lg text-muted-foreground">
          Have questions or need assistance? We're here to help!
        </p>
        <p className="text-lg text-muted-foreground">
          Contact us via email:{" "}
          <a
            href="mailto:<EMAIL>"
            className="text-primary hover:underline"
          >
            <EMAIL>
          </a>
        </p>
      </div>

      <div className="w-full max-w-3xl bg-secondary/5 backdrop-blur-sm rounded-2xl p-8 border border-primary/10">
        <form className="flex flex-col gap-6">
          <Input
            type="text"
            id="name"
            name="name"
            label="Name"
            placeholder="Your name"
            className="w-full"
          />

          <Input
            type="email"
            id="email"
            name="email"
            label="Email"
            placeholder="Your email"
            className="w-full"
          />

          <Select
            id="subject"
            name="subject"
            label="Subject"
            placeholder="Choose a subject"
            className="w-full"
          >
            <SelectItem key="general">General Inquiry</SelectItem>
            <SelectItem key="billing">Billing Issue</SelectItem>
            <SelectItem key="technical">Technical Support</SelectItem>
            <SelectItem key="other">Other</SelectItem>
          </Select>

          <Textarea
            id="message"
            name="message"
            label="Message"
            placeholder="Your message here..."
            minRows={6}
            className="w-full"
          />

          <Button
            type="submit"
            className="py-3 px-6 rounded-xl text-lg font-medium transition-all duration-300"
            variant="shadow"
            color="primary"
          >
            Submit
          </Button>
        </form>
      </div>
    </div>
  );
};

export default ContactUs;