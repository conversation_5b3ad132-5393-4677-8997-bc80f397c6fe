import React from "react";
import { Metada<PERSON> } from "next";
import <PERSON><PERSON><PERSON> from "next/script";

export const metadata: Metadata = {
  title: "Terms of Service | Burst Mode AI Photography",
  description:
    "Read Burst Mode's terms of service agreement. Learn about user accounts, content ownership, prohibited conduct, and your legal rights and responsibilities when using our AI photography platform.",
  keywords: [
    "terms of service",
    "terms and conditions",
    "user agreement",
    "legal terms",
    "service terms",
    "Burst Mode terms",
    "AI photography terms",
    "user rights",
    "content ownership",
    "service conditions",
  ],
  openGraph: {
    title: "Terms of Service | Burst Mode AI Photography",
    description:
      "Read Burst Mode's terms of service agreement. Learn about user accounts, content ownership, prohibited conduct, and your legal rights and responsibilities when using our AI photography platform.",
    url: "https://burstmode.ai/terms",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Terms of Service",
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Terms of Service | Burst Mode AI Photography",
    description:
      "Read Burst Mode's terms of service agreement and learn about your rights and responsibilities.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Terms of Service",
      },
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/terms",
  },
};

const Terms: React.FC = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="terms-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            name: "Terms of Service | Burst Mode AI Photography",
            description:
              "Read Burst Mode's terms of service agreement. Learn about user accounts, content ownership, prohibited conduct, and your legal rights and responsibilities when using our AI photography platform.",
            url: "https://burstmode.ai/terms",
            lastReviewed: "2025-03-18",
            mainContentOfPage: {
              "@type": "WebPageElement",
              cssSelector: ".container",
            },
            speakable: {
              "@type": "SpeakableSpecification",
              cssSelector: ["h1", "h2", "p"],
            },
            publisher: {
              "@type": "Organization",
              name: "Burst Mode AI",
              url: "https://burstmode.ai",
              logo: {
                "@type": "ImageObject",
                url: "https://burstmode.ai/app/icon-transparent.png",
              },
            },
          }),
        }}
      />
      <div className="container mx-auto px-6 py-12 max-w-3xl pt-20 text-gray-300">
        <h1 className="text-3xl font-bold mb-6">
          Burst Mode Terms and Services
        </h1>
        <p className="text-gray-400 mb-4">Last Updated: 03/18/2025</p>

        <p className="mb-6">
          Welcome to Burst Mode AI (the "Service"), provided by Krysko Holding
          LLC ("we," "us," or "our"). By accessing or using our Service, you
          ("you" or "user") agree to be bound by these Terms and Services
          ("Terms"). Please read them carefully.
        </p>
        {/* Section 1: Acceptance of Terms */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">
            1. Acceptance of Terms
          </h2>
          <p className="mb-2">
            By using our Service, you confirm that you are at least 18 years old
            and have the legal capacity to enter into these Terms. If you are
            using the Service on behalf of an organization, you represent that
            you have the authority to bind that organization to these Terms. If
            you do not agree to these Terms, you must not use our Service.
          </p>
        </section>

        {/* Section 2: Description of Service */}

        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">
            2. Description of Service:
          </h2>
          <p className="mb-2">
            Burst Mode is an AI-powered photography web application that allows
            users to generate images from text prompts, enhance existing photos,
            apply stylistic filters, etc.
          </p>
        </section>
        {/* Section 3: User Accounts*/}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">3. User Accounts:</h2>
          <ul>
            <li>
              You may be required to create an account to access certain
              features of the Service.
            </li>
            <li>
              You are responsible for maintaining the confidentiality of your
              account credentials.
            </li>
            <li>
              You agree to provide accurate, current, and complete information
              during registration and to update such information to keep it
              accurate, current, and complete.
            </li>
            <li>
              You are responsible for all activities that occur under your
              account.
            </li>
          </ul>
        </section>
        {/* Section 4: User Content */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">4. User Content:</h2>
          <ul>
            <li>
              You retain ownership of the content you upload to our Service
              ("User Content").
            </li>
            <li>
              By uploading User Content, you grant us a non-exclusive,
              worldwide, royalty-free license to use, reproduce, modify, adapt,
              publish, and display such User Content for the purpose of
              providing and improving the Service.
            </li>
            <li>
              You represent and warrant that you own or have the necessary
              rights to grant us the licenses to your User Content.
            </li>
            <li>
              You are solely responsible for your User Content and the
              consequences of posting or publishing it.
            </li>
            <li>
              You agree not to upload any content that is illegal, harmful,
              offensive, or infringing on the rights of others.
            </li>
          </ul>
        </section>
        {/* Section 5: AI-Generated Content */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">
            5. AI-Generated Content:
          </h2>
          <ul>
            <li>
              The Service utilizes AI algorithms to generate images based on
              user prompts.
            </li>
            <li>
              You acknowledge that AI-generated content may not always be
              accurate, reliable, or suitable for all purposes.
            </li>
            <li>
              We do not claim ownership of AI-generated content, but we retain
              the right to use it for improving our Service.
            </li>
            <li>
              You are responsible for the use of AI-generated content and any
              potential legal implications.
            </li>
            <li>
              You agree to use generated content responsibly and ethically.
            </li>
          </ul>
        </section>

        {/* Section 6: Prohibited Conduct */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">
            6. Prohibited Conduct:
          </h2>
          <p>You agree not to:</p>
          <ul>
            <li>Use the Service for any illegal or unauthorized purpose.</li>
            <li>Attempt to interfere with or disrupt the Service.</li>
            <li>Use the Service to transmit viruses or other harmful code.</li>
            <li>
              Collect or harvest any personally identifiable information from
              other users.
            </li>
            <li>
              Reverse engineer or attempt to extract the source code of the
              Service.
            </li>
            <li>
              Use the Service to generate content that promotes hate speech,
              violence, or discrimination.
            </li>
            <li>
              Use the service to generate content that depicts child
              exploitation.
            </li>
          </ul>
        </section>

        {/* Section 7: Intellectual Property*/}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">
            7. Intellectual Property:
          </h2>
          <p>
            The Service and its content (excluding User Content) are owned by us
            or our licensors and are protected by intellectual property laws.
            You may not reproduce, distribute, or modify any part of the Service
            without our express written consent.
          </p>
        </section>

        {/* Section 8: Disclaimer of Warranties */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">
            8. Disclaimer of Warranties:
          </h2>
          <p>
            The Service is provided "as is" and "as available" without
            warranties of any kind, either express or implied. We do not warrant
            that the Service will be uninterrupted, error-free, or secure. We do
            not warrant the accuracy, completeness, or reliability of any
            content generated by the Service.
          </p>
        </section>

        {/* Section 9: Limitation of Liability */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">
            9. Limitation of Liability:
          </h2>
          <p>
            In no event shall we be liable for any direct, indirect, incidental,
            consequential, or punitive damages arising out of your use of the
            Service. Our liability shall be limited to the amount you paid for
            the Service, if any.
          </p>
        </section>

        {/* Section 10: Indemnification */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">10. Indemnification:</h2>
          <p>
            You agree to indemnify and hold us harmless from any claims,
            damages, or expenses arising out of your use of the Service or your
            violation of these Terms.
          </p>
        </section>

        {/* Section 11: Changes to Terms */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">11. Changes to Terms:</h2>
          <p>
            We reserve the right to modify these Terms at any time. We will
            notify you of any changes by posting the new Terms on the Service.
            Your continued use of the Service after such changes constitutes
            your acceptance of the new Terms.
          </p>
        </section>

        {/* Section 12: Governing Law*/}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">12. Governing Law:</h2>
          <p>
            These Terms shall be governed by and construed in accordance with
            the laws of New Jersey.
          </p>
        </section>

        {/* Section 13: Contact Us */}
        <section className="mb-8">
          <h2 className="text-2xl font-semibold mb-4">13. Contact Us:</h2>
          <p>
            If you have any questions about these Terms, please contact us at{" "}
            <a href="mailto:<EMAIL>"><EMAIL></a>.
          </p>
        </section>
      </div>
    </>
  );
};

export default Terms;