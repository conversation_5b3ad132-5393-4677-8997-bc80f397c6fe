"use server";
import { Resend } from "resend";
import { LowCreditEmailTemplate } from "@/emailTemplates/LowCreditEmailTemplate";

const resend = new Resend(process.env.RESEND_API_KEY as string);

export async function sendLowCreditEmail(
  userName: string,
  amount: number,
  to: string,
  totalCredits: number
) {
  try {
    const emailHtml = await LowCreditEmailTemplate(userName, amount, totalCredits);

    const { data, error } = await resend.emails.send({
      from: "BurstMode.ai <<EMAIL>>",
      to: [to],
      subject: "Low Credits Alert",
      html: emailHtml,
    });

    if (error) {
      console.error("Error sending email:", error.message);
      throw new Error(error.message);
    }

    if (data) {
      console.log("Low credit email sent successfully:", data);
    }
  } catch (error: any) {
    console.error("Error in sendLowCreditEmail function:", error);
  }
}
