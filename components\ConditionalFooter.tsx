"use client";

import { usePathname } from "next/navigation";
import Footer from "@/components/Footer";

const authPages = ["/login", "/register"];

export default function ConditionalFooter() {
  const pathname = usePathname() || "";

  const isAuthPage = authPages.some(
    (page) => pathname === page || pathname.startsWith(page)
  );

  if (isAuthPage) {
    return null;
  }

  return <Footer />;
}