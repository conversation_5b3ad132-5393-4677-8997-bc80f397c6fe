'use client';

import React from 'react';
import { ArrowRight, Check } from 'lucide-react';

const AboutPage = () => {
  return (
    <div className="w-full flex flex-col items-center justify-center">
      {/* Hero Section */}
      <div className="w-full flex items-center flex-col justify-center px-4 py-16 lg:px-8 lg:py-24 gap-12">
        <div className="text-center space-y-4 max-w-3xl">
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-primary to-secondary/70 bg-clip-text text-transparent">
            About Burst Mode
          </h1>
          <p className="text-lg text-muted-foreground leading-relaxed">
            Welcome to Burst Mode, your go-to destination for stunning, AI-powered photo generation. 
            We believe everyone deserves to showcase their best self through exceptional imagery.
          </p>
        </div>

        {/* Capabilities Section */}
        <div className="w-full max-w-4xl grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
          {[
            {
              title: "Enhanced Headshots",
              description: "Professional polish with smooth skin and bright eyes",
            },
            {
              title: "Product Showcase",
              description: "Vibrant, high-resolution product imagery",
            },
            {
              title: "Food Photography",
              description: "Make culinary creations look irresistible",
            },
          ].map((item, index) => (
            <div 
              key={index}
              className="p-6 rounded-2xl bg-secondary/5 backdrop-blur-sm border border-primary/10 hover:border-primary/30 transition-all duration-300"
            >
              <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
              <p className="text-muted-foreground">{item.description}</p>
            </div>
          ))}
        </div>

        {/* Mission Section */}
        <div className="w-full max-w-3xl bg-secondary/5 backdrop-blur-sm rounded-2xl p-8 border border-primary/10 mt-12">
          <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
          <p className="text-lg text-muted-foreground leading-relaxed">
            To empower individuals and businesses with the tools to present their best visual story. 
            We strive to provide a seamless and intuitive user experience, delivering exceptional 
            results with speed and efficiency.
          </p>
        </div>

        {/* Team Section */}
        <div className="w-full max-w-4xl mt-12">
          <h2 className="text-3xl font-bold text-center mb-8">Our Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              "AI/ML Engineers",
              "UX Designers",
              "Photography Experts"
            ].map((role, index) => (
              <div 
                key={index}
                className="p-6 rounded-2xl bg-secondary/5 backdrop-blur-sm border border-primary/10 text-center hover:scale-105 transition-all duration-300"
              >
                <div className="h-20 w-20 mx-auto mb-4 rounded-full bg-primary/10 flex items-center justify-center">
                  <div className="h-12 w-12 rounded-full bg-primary/20" />
                </div>
                <h3 className="font-semibold">{role}</h3>
              </div>
            ))}
          </div>
        </div>

        {/* Values Section */}
        <div className="w-full max-w-3xl mt-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              "Innovation First",
              "User-Centric Design",
              "Quality Results",
              "Continuous Improvement"
            ].map((value, index) => (
              <div key={value} className="flex items-center gap-2">
                <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                  <Check className="h-4 w-4 text-primary" />
                </div>
                <span className="text-lg font-medium">{value}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;