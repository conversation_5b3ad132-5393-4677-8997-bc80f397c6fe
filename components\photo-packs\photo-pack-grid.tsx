import { AstriaPacksService } from "@/lib/astria-packs";
import { PhotoPackCard } from "./photo-pack-card";

const photoPacks = [
  {
    id: 1,
    title: "Instagram",
    description: "Perfect for your social feed",
    image: "/_ai_packs/_pack2.png",
    tag: "Popular",
  },
  {
    id: 2,
    title: "Instant Camera",
    description:
      "Vintage vibes with modern quality Vintage vibes with modern quality",
    image: "/_ai_packs/_pack1.png",
    tag: "Trending",
  },
  {
    id: 3,
    title: "Tinder",
    description: "Stand out in the dating scene.",
    image: "/_ai_packs/_pack18.png",
    tag: "Featured",
  },
  {
    id: 4,
    title: "Model Headshots",
    description: "Professional portfolio quality",
    image: "/_ai_packs/_pack11.png",
    tag: "Premium",
  },
  {
    id: 5,
    title: "Luxury Lifestyle",
    description: "Elevate your personal brand.",
    image: "/_ai_packs/_pack12.png",
    tag: "Exclusive",
  },
  {
    id: 6,
    title: "Boudoir",
    description: "Tasteful, elegant portraits",
    image: "/_ai_packs/_pack9.png",
    tag: "Private",
  },
  {
    id: 7,
    title: "LinkedIn Headshot",
    description: "Professional and approachable",
    image: "/_ai_packs/_pack21.png",
    tag: "Business",
  },
  {
    id: 8,
    title: "Avatar AI",
    description: "Digital persona creation",
    image: "/_ai_packs/_pack20.png",
    tag: "New",
  },
  {
    id: 9,
    title: "Food Photography",
    description: "Make your culinary creations shine",
    image: "/_ai_packs/_pack4.png",
    tag: "Tasty",
  },
  {
    id: 10,
    title: "AI Fashion",
    description:
      "Cutting-edge style transformations for the modern digital wardrobe",
    image: "/_ai_packs/_pack13.png",
    tag: "Innovative",
  },
];

export async function PhotoPackGrid() {
  const astriaPacksService = new AstriaPacksService();
  const pack = await astriaPacksService.getAllPacks();

  const astriaPacks = pack.data;
  const astriaPacksWithImage = photoPacks?.map((pack) => {
    const astriaData = astriaPacks?.find((curr) =>
      curr.title.toLowerCase().includes(pack.title.toLowerCase())
    );
    return {
      ...pack,
      id: astriaData?.id ? astriaData.id : undefined,
      base_tune_id: astriaData?.base_tune_id,
    };
  });

  return (
    <section className="w-full px-3 lg:px-8 xl:px-28 pb-20">
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold tracking-tight mb-4 bg-gradient-to-r from-white to-white/50 bg-clip-text text-transparent">
          Discover Your Perfect Style
        </h2>
        <p className="text-lg max-w-2xl mx-auto bg-gradient-to-l from-white to-white/50 bg-clip-text text-transparent">
          Choose from our curated collection of premium photo styles to
          transform your look
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 auto-rows-auto ">
        {astriaPacksWithImage?.map((pack, idx) => (
          <div key={idx} className="grid-item">
            <PhotoPackCard pack={pack} />
          </div>
        ))}
      </div>
    </section>
  );
}
