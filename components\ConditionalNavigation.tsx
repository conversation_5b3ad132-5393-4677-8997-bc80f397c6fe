"use client";

import { usePathname } from "next/navigation";
import Navbar from "@/components/navbar";
import MobileSidebarWrapper from "@/components/ui/MobileSidebarWrapper";
import Footer from "@/components/Footer";

const authPages = ["/login", "/register"];

export default function ConditionalNavigation() {
  const pathname = usePathname() || "";

  const isAuthPage = authPages.some(
    (page) => pathname === page || pathname.startsWith(page)
  );

  if (isAuthPage) {
    return null;
  }

  return (
    <>
      <Navbar />
      <MobileSidebarWrapper />
    </>
  );
}
