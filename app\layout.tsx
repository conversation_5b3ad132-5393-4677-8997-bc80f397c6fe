import "@/styles/globals.css";
import { Metadata, Viewport } from "next";
import clsx from "clsx";

import { Providers } from "./providers";

import { fontSans } from "@/config/fonts";
import ConditionalNavigation from "@/components/ConditionalNavigation";
import ConditionalFooter from "@/components/ConditionalFooter";

export const metadata: Metadata = {
  title: "Burst Mode - Professional AI Photography & Image Generation",
  description:
    "Transform your photos with Burst Mode's AI-powered tools. Create professional headshots, enhance food & product photography, and generate stunning visuals for social media and marketing. Save time and money with our easy-to-use AI photography platform.",
  keywords: [
    "AI photography",
    "artificial intelligence photography",
    "AI photo generation",
    "AI image generation",
    "AI photo editing",
    "AI photo enhancement",
    "AI image manipulation",
    "AI generated photos",
    "AI photo tools",
    "AI photography software",
    "professional headshots AI",
    "food photography enhancement",
    "product photography AI",
    "best AI photo generators",
    "AI photography for e-commerce",
    "AI photography for social media",
    "AI photography for marketing",
    "generate photos with AI",
    "edit photos with AI",
    "enhance photos with AI",
    "AI photo generation online",
    "AI photo editor online",
    "AI image generator from text",
    "virtual try-on AI",
    "image to video AI",
  ],
  metadataBase: new URL("https://burstmode.ai"),
  authors: [{ name: "Krysko Holding LLC" }],
  creator: "Burst Mode AI",
  publisher: "Burst Mode AI",
  category: "AI Photography",
  applicationName: "Burst Mode",
  openGraph: {
    type: "website",
    locale: "en_US",
    title: "Burst Mode - Professional AI Photography & Image Generation",
    description:
      "Transform your photos with Burst Mode's AI-powered tools. Create professional headshots, enhance food & product photography, and generate stunning visuals for social media and marketing. Save time and money with our easy-to-use AI photography platform.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography",
      },
    ],
    url: "https://burstmode.ai",
    siteName: "Burst Mode AI",
  },
  twitter: {
    card: "summary_large_image",
    title: "Burst Mode - Professional AI Photography & Image Generation",
    description:
      "Transform your photos with Burst Mode's AI-powered tools. Create professional headshots, enhance food & product photography, and generate stunning visuals for social media and marketing.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography",
      },
    ],
    creator: "@burstmodeai",
    site: "@burstmodeai",
  },
  alternates: {
    canonical: "https://burstmode.ai",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-video-preview": -1,
      "max-snippet": -1,
    },
  },
  verification: {
    // Add your verification codes here when you have them
    // google: "your-google-verification-code",
    // yandex: "your-yandex-verification-code",
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pageTitle = metadata.title || "Burst Mode - AI Photography";

  return (
    <html suppressHydrationWarning lang="en">
      <head>
        <title>{String(pageTitle)}</title>
        <link rel="icon" href="/app/icon-transparent.png" type="image/png" />
        <link rel="apple-touch-icon" href="/app/icon-transparent.png" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#000000" />
        <link rel="canonical" href="https://burstmode.ai" />
        <link rel="manifest" href="/manifest.json" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              name: "Burst Mode AI",
              url: "https://burstmode.ai",
              description:
                "AI-powered photography enhancement and generation platform",
              applicationCategory: "Photography",
              operatingSystem: "Web",
              offers: {
                "@type": "Offer",
                price: "15.00",
                priceCurrency: "USD",
              },
            }),
          }}
        />
      </head>
      <body
        className={clsx(
          "min-h-screen text-foreground bg-background font-sans antialiased",
          fontSans.variable
        )}
      >
        <Providers themeProps={{ attribute: "class", defaultTheme: "dark" }}>
          <div className="flex flex-col min-h-screen">
            <ConditionalNavigation />
            <main className="flex-grow">{children}</main>
            <ConditionalFooter />
          </div>
        </Providers>
      </body>
    </html>
  );
}
