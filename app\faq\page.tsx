import FAQs from "@/components/FAQs";
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "Frequently Asked Questions | Burst Mode AI Photography",
  description: "Find answers to common questions about Burst Mode's AI photography services, including photo enhancement, pricing, privacy, and technical requirements.",
  keywords: [
    "Burst Mode FAQ",
    "AI photography questions",
    "Burst Mode help",
    "AI image generation FAQ",
    "photo enhancement questions",
    "Burst Mode pricing FAQ",
    "AI headshot questions",
    "Burst Mode subscription FAQ"
  ],
  openGraph: {
    title: "Frequently Asked Questions | Burst Mode AI Photography",
    description: "Find answers to common questions about Burst Mode's AI photography services, including photo enhancement, pricing, privacy, and technical requirements.",
    url: "https://burstmode.ai/faq",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography FAQ",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Frequently Asked Questions | Burst Mode AI Photography",
    description: "Find answers to common questions about Burst Mode's AI photography services.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography FAQ",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/faq",
  },
};

const FaqPage = () => {
  return (
    <>
      {/* Structured Data for FAQ Page */}
      <Script
        id="faq-page-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": "What types of photos can I generate and enhance with Burst Mode?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Headshots, Product photos, Food photos"
                }
              },
              {
                "@type": "Question",
                "name": "How does Burst Mode enhance my photos?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Skin smoothing: Reduces blemishes and wrinkles for natural-looking skin. Eye enhancement: Brightens eyes and removes any redness. Color correction: Adjusts color balance and enhances vibrancy. Background refinement: Improves background quality and removes distractions. Sharpness and clarity: Increases sharpness and clarity for sharper images."
                }
              },
              {
                "@type": "Question",
                "name": "Is Burst Mode easy to use?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Absolutely! Our platform is designed to be user-friendly and accessible to everyone. Simply upload your photos, and our AI will do the rest."
                }
              },
              {
                "@type": "Question",
                "name": "What are the system requirements?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "A stable internet connection. A modern web browser (Chrome, Firefox, Safari, Edge)."
                }
              },
              {
                "@type": "Question",
                "name": "How much does Burst Mode cost?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Please see our Pricing section."
                }
              },
              {
                "@type": "Question",
                "name": "How can I contact support?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "You can contact our support team by writing <NAME_EMAIL> or by using the contact form on our Contact Us page."
                }
              }
            ]
          })
        }}
      />
      <FAQs />
    </>
  );
}

export default FaqPage;