"use server";
import nodemailer from "nodemailer";
import { sendEmail } from "./SendEmail";

type SuggestFeatureData = {
  name: string;
  email: string;
  category: string;
  description: string;
  priority: string;
};

const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    type: "OAuth2",
    user: "<EMAIL>",
    clientId: "56754996013-gpqao3taujj5i9hlnjjnj1sssko0ib6f.apps.googleusercontent.com",
    clientSecret: "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",
    refreshToken:
      "1//04ECCW3CEdC4RCgYIARAAGAQSNwF-L9Irxmjpo6rcg_Xg1fnN5LimSH5BHX-Gx0b-SPMhjZOPWb52R3BeKEGmmJNCovegL2oD8eU",
  },
});


export const suggestAFeatureAction = async (data: SuggestFeatureData) => {

  try {
    const html = await sendEmail(data);
    const options = {
      from: '"Burstmode AI" <<EMAIL>>', 
      to: "<EMAIL>", 
      subject: "New Feature Suggestion",
      html: html,
    };

    console.log("Attempting to send email...");
    const info = await transporter.sendMail(options); 
    console.log("Email sent successfully!");
    console.log("Message ID:", info.messageId);

    return {
      message: "Thank you for your feedback. We'll review your suggestion.",
    };
  } catch (error:any) {
    console.error("Error in suggestAFeatureAction:", error);
    throw new Error(error.message);
  }
};