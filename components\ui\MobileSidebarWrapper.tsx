"use client";

import React from "react";
import {
  Links,
  MobileSidebar,
  SidebarLink,
  useSidebar,
} from "@/components/ui/Sidebar";
import {
  LogIn,
  Home,
  DollarSign,
  Bot,
  LogOut,
  FileText,
  Lightbulb,
} from "lucide-react";

import { useUser } from "@/context/UserContext";
import { cn } from "@/lib/utils";
import Link from "next/link";
import Image from "next/image";
import { Poppins } from "next/font/google";
const font = Poppins({ weight: "600", subsets: ["latin"] });

const MobileSidebarWrapper = () => {
  const { setOpen } = useSidebar();
  const { user, logout } = useUser();

  const links = [
    {
      label: "Home",
      href: "/",
      icon: (
        <Home className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    {
      label: "Tools",
      href: "/model",
      icon: (
        <Bot className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    {
      label: "Pricing",
      href: "/pricing",
      icon: (
        <DollarSign className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    {
      label: "Blog",
      href: "/blog",
      icon: (
        <FileText className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    {
      label: "Suggest Feature",
      href: "/suggest-feature",
      icon: (
        <Lightbulb className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
    user && {
      label: "Logout",
      href: "/",
      icon: (
        <LogOut className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => {
        logout();
        setOpen(false);
      },
    },
    !user && {
      label: "Login",
      href: "/login",
      icon: (
        <LogIn className="text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0" />
      ),
      onClick: () => setOpen(false),
    },
  ].filter(Boolean) as Links[];

  return (
    <MobileSidebar>
      <div>
        <Link href="/" className="absolute top-8 left-7">
          <div className="flex items-center gap-x-2">
            <Image
              src="/app/icon-transparent.png"
              alt="Burst Mode Icon"
              width={40}
              height={40}
              className="object-contain"
            />
            <h1
              className={cn(
                "text-xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 via-blue-500 to-purple-400 bg-clip-text text-transparent animate-[gradient-slide_5s_linear_infinite] bg-[length:200%_200%]",
                font.className
              )}
            >
              Burst Mode
            </h1>
          </div>
        </Link>
        <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
          {/* {open ? <Logo /> : <LogoIcon />} */}
          <div className="mt-8 flex flex-col gap-2">
            {links.map((link, idx) => (
              <SidebarLink key={idx} link={link} onClick={link.onClick} />
            ))}
          </div>
        </div>
      </div>
    </MobileSidebar>
  );
};

export default MobileSidebarWrapper;
