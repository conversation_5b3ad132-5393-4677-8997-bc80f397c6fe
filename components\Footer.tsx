import Link from "next/link";
import React from "react";

const Footer = () => {
  return (
    <footer className="bg-[#111] text-gray-300 relative mt-5">
      <div className="container mx-auto px-4 py-12 lg:px-8 lg:py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          <div className="flex flex-col space-y-6">
            <p className="font-bold text-2xl text-white">Burst Mode</p>
            <div className="flex gap-4">
              <Link
                href="https://instagram.com/burstmodeai"
                target="_blank"
                rel="noreferrer"
                className="hover:text-white transition-colors"
                aria-label="Instagram"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-5 h-5"
                >
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line>
                </svg>
              </Link>

              <Link
                href="https://www.facebook.com/share/12JUmhjJ8hB/"
                target="_blank"
                rel="noreferrer"
                className="hover:text-white transition-colors"
                aria-label="Facebook"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                >
                  <path
                    d="M22 12.07C22 6.49 17.52 2 12 2S2 6.49 2 12.07c0 5.02 3.66 9.18 8.44 9.92v-7.02H7.9v-2.9h2.54v-2.2c0-2.5 1.5-3.87 3.77-3.87 1.1 0 2.25.2 2.25.2v2.46h-1.27c-1.25 0-1.63.77-1.63 1.56v1.85h2.78l-.44 2.9h-2.34v7.02C18.34 21.25 22 17.1 22 12.07z"
                    fill="white"
                  />
                </svg>
              </Link>

              <Link
                href="https://twitter.com/burstmodeai"
                target="_blank"
                rel="noreferrer"
                className="hover:text-white transition-colors"
                aria-label="Twitter"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 1200 1227"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                >
                  <path
                    d="M714.163 519.284L1160.89 0H1055.03L667.137 450.887L357.328 0H0L468.492 681.821L0 1226.37H105.866L515.491 750.218L842.672 1226.37H1200L714.137 519.284H714.163ZM569.165 687.828L521.697 619.934L144.011 79.6944H306.615L611.412 515.685L658.88 583.579L1055.08 1150.3H892.476L569.165 687.854V687.828Z"
                    fill="white"
                  />
                </svg>
              </Link>

              <Link
                href="https://www.youtube.com/@burstmodeai"
                target="_blank"
                rel="noreferrer"
                className="hover:text-white transition-colors"
                aria-label="YouTube"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="white"
                  className="w-5 h-5"
                >
                  <path d="M22.54 6.42a2.78 2.78 0 0 0-1.95-1.96C18.21 4 12 4 12 4s-6.21 0-8.59.46A2.78 2.78 0 0 0 1.46 6.42C1 8.8 1 12 1 12s0 3.2.46 5.58a2.78 2.78 0 0 0 1.95 1.96C5.79 20 12 20 12 20s6.21 0 8.59-.46a2.78 2.78 0 0 0 1.95-1.96C23 15.2 23 12 23 12s0-3.2-.46-5.58zM9.75 15.02V8.98L15.52 12l-5.77 3.02z" />
                </svg>
              </Link>

              <Link
                href="https://www.tiktok.com/@burstmodeai"
                target="_blank"
                rel="noreferrer"
                className="hover:text-white transition-colors"
                aria-label="TikTok"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="white"
                  className="w-5 h-5"
                >
                  <path d="M16 8.25v-2.8a4.91 4.91 0 0 1-1.5-3.2h-3.1v12.9a2.59 2.59 0 1 1-1.8-2.48V8.64a6.53 6.53 0 1 0 6.4 6.36V8.25z"></path>
                </svg>
              </Link>
            </div>
          </div>
          <div className="col-span-1 md:col-span-3 grid grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="space-y-4">
              <h2 className="font-semibold text-white mb-3">Tools</h2>
              <ul className="space-y-2">
                <li>
                  <Link href="/model/headshots" className="hover:text-white transition-colors">
                    AI Photoshoot
                  </Link>
                </li>
                <li>
                  <Link href="/model/foods" className="hover:text-white transition-colors">
                    Food Photography
                  </Link>
                </li>
                <li>
                  <Link href="/model/products" className="hover:text-white transition-colors">
                    Product Shoots
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h2 className="font-semibold text-white mb-3">Use Cases</h2>
              <ul className="space-y-2">
                <li>
                  <Link href="/model" className="hover:text-white transition-colors">
                    AI Generator
                  </Link>
                </li>
                <li>
                  <Link href="/model" className="hover:text-white transition-colors">
                    Artistic Filters
                  </Link>
                </li>
                <li>
                  <Link href="/model/virtual-try-on" className="hover:text-white transition-colors">
                    Virtual Try-on
                  </Link>
                </li>
                {/* <li>
                  <Link href="/model" className="hover:text-white transition-colors">
                    Furry Friends
                  </Link>
                </li> */}
                <li>
                  <Link href="/model/upscaling" className="hover:text-white transition-colors">
                    Upscaling
                  </Link>
                </li>
                <li>
                  <Link href="/model/interior" className="hover:text-white transition-colors">
                    Interior Design
                  </Link>
                </li>
                <li>
                  <Link href="/model/image-to-video" className="hover:text-white transition-colors">
                    Video Generator
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h2 className="font-semibold text-white mb-3">Company</h2>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/privacy-policy"
                    className="hover:text-white transition-colors"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="hover:text-white transition-colors">
                    Terms and Services
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="hover:text-white transition-colors">
                    Blog
                  </Link>
                </li>
                {/* <li>
                  <Link href="#" className="hover:text-white transition-colors">
                    Affiliate Program
                  </Link>
                </li> */}
                <li>
                  <Link href="/suggest-feature" className="hover:text-white transition-colors">
                    Suggest a Feature
                  </Link>
                </li>
              </ul>
            </div>
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="font-semibold text-white mb-3">Resources</h2>
                <ul className="space-y-2">
                  <li>
                    <Link
                      href="/about-us"
                      className="hover:text-white transition-colors"
                    >
                      About Us
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/contact-us"
                      className="hover:text-white transition-colors"
                    >
                      Contact Us
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/#how-it-works"
                      className="hover:text-white transition-colors"
                    >
                      Guides
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/faq"
                      className="hover:text-white transition-colors"
                    >
                      FAQ
                    </Link>
                  </li>
                  {/* <li>
                    <Link
                      href="#"
                      className="hover:text-white transition-colors"
                    >
                      AI Models
                    </Link>
                  </li> */}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
