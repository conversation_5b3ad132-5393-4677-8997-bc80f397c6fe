import { auth, db } from "@/lib/firebase";
import { GoogleAuthProvider, signInWithPopup } from "firebase/auth";
import { doc, getDoc, setDoc } from "firebase/firestore";

/**
 * Sign in with Google
 * @returns { success: boolean; user?: { email: string | null; fullname: string | null; uid: string }; message: string }
 */
export const signInWithGoogle = async (): Promise<{
  success: boolean;
  user?: { email: string | null; fullname: string | null; uid: string };
  message: string;
}> => {
  try {
    const provider = new GoogleAuthProvider();

    // You can use `signInWithPopup` or `signInWithRedirect`
    const result = await signInWithPopup(auth, provider);
    const user = result.user;

    // Check if user already exists in Firestore
    const userDocRef = doc(db, "users", user.uid);
    const userDoc = await getDoc(userDocRef);

    if (!userDoc.exists()) {
      // If user is new, save to Firestore
      await setDoc(userDocRef, {
        fullname: user.displayName || "Google User",
        email: user.email,
        photoUrl: user.photoURL,
      });
    }

    return {
      success: true,
      user: {
        uid: user.uid,
        email: user.email,
        fullname: user.displayName || "Google User",
      },
      message: "Google sign-in successful",
    };
  } catch (error: any) {
    console.error("Google Sign-In Error:", error);
    return {
      success: false,
      message: error.message || "Google sign-in failed",
    };
  }
};
