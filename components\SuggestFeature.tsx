"use client";

import React, { useState, useTransition } from "react";
import { But<PERSON> } from "@heroui/button";
import { addToast } from "@heroui/toast";
import { Select, SelectItem } from "@heroui/select";
import { Input, Textarea } from "@heroui/input";
import { <PERSON>bul<PERSON>, <PERSON>rkles, ThumbsUp } from "lucide-react";
import { suggestAFeatureAction } from "@/actions/suggestAFeatureAction";

const SuggestFeature = () => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [category, setCategory] = useState("");
  const [description, setDescription] = useState("");
  const [priority, setPriority] = useState("");

  const [isPending, startTransition] = useTransition();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    startTransition(() => {
      suggestAFeatureAction({
        name,
        email,
        category,
        description,
        priority,
      })
        .then((res) => {
          addToast({
            title: "Feature suggestion submitted!",
            description: res.message,
            color: "success",
          });
        })
        .catch((err) => {
          addToast({
            title: "Error",
            description: err.message,
            color: "danger",
          });
        });
    });
  };

  return (
    <div className="w-full flex items-center flex-col justify-center px-4 py-16 lg:px-8 lg:py-24 gap-12">
      <div className="text-center space-y-4 max-w-3xl">
        <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Suggest a Feature
        </h1>
        <p className="text-lg text-muted-foreground">
          Have an idea to make Burst Mode even better? We'd love to hear it!
        </p>
        <p className="text-lg text-muted-foreground">
          Your suggestions help us prioritize new features and improvements.
        </p>
      </div>

      {/* Feature suggestion benefits */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-3xl">
        <div className="bg-secondary/5 backdrop-blur-sm rounded-lg p-6 border border-primary/10 flex flex-col items-center text-center">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <Lightbulb className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Share Your Ideas</h3>
          <p className="text-sm text-muted-foreground">
            Help shape the future of Burst Mode with your creative suggestions
          </p>
        </div>

        <div className="bg-secondary/5 backdrop-blur-sm rounded-lg p-3 sm:p-6 border border-primary/10 flex flex-col items-center text-center">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <Sparkles className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-semibold mb-2">
            Enhance Your Experience
          </h3>
          <p className="text-sm text-muted-foreground">
            Get the features you need to make your AI photography workflow even
            better
          </p>
        </div>

        <div className="bg-secondary/5 backdrop-blur-sm rounded-lg p-6 border border-primary/10 flex flex-col items-center text-center">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <ThumbsUp className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Be Heard</h3>
          <p className="text-sm text-muted-foreground">
            We value your input and actively review all feature suggestions
          </p>
        </div>
      </div>

      <div className="w-full max-w-3xl bg-secondary/5 backdrop-blur-sm rounded-2xl p-4 md:p-8 border border-primary/10">
        <form onSubmit={handleSubmit} className="flex flex-col gap-6">
          <div className="flex flex-col sm:flex-row gap-6">
            <Input
              type="text"
              id="name"
              name="name"
              label="Name"
              placeholder="Your name"
              value={name}
              onValueChange={setName}
              required
              className="flex-1"
            />
            <Input
              type="email"
              id="email"
              name="email"
              label="Email"
              placeholder="Your email"
              value={email}
              onValueChange={setEmail}
              required
              className="flex-1"
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-6">
            <Select
              id="category"
              name="category"
              label="Feature Category"
              placeholder="Select category"
              selectedKeys={category ? [category] : []}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                setCategory(value);
              }}
              required
              className="flex-1"
            >
              <SelectItem key="ui">User Interface</SelectItem>
              <SelectItem key="generation">Image Generation</SelectItem>
              <SelectItem key="enhancement">Image Enhancement</SelectItem>
              <SelectItem key="video">Video Features</SelectItem>
              <SelectItem key="integration">Integrations</SelectItem>
              <SelectItem key="other">Other</SelectItem>
            </Select>
            <Select
              id="priority"
              name="priority"
              label="Priority Level"
              placeholder="Select priority"
              selectedKeys={priority ? [priority] : []}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                setPriority(value);
              }}
              required
              className="flex-1"
            >
              <SelectItem key="low">Nice to Have</SelectItem>
              <SelectItem key="medium">Important</SelectItem>
              <SelectItem key="high">Critical</SelectItem>
            </Select>
          </div>

          <Textarea
            id="description"
            name="description"
            label="Feature Description"
            placeholder="Describe your feature idea in detail. What problem would it solve? How would it improve your experience?"
            value={description}
            onValueChange={setDescription}
            minRows={6}
            required
            className="w-full"
          />

          <Button
            type="submit"
            disabled={isPending}
            className="text-xs sm:text-sm md:text-lg  py-3 px-6 rounded-lg font-medium transition-all duration-300"
            variant="shadow"
            color="primary"
          >
            {isPending ? "Submitting..." : "Submit Feature Suggestion"}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default SuggestFeature;